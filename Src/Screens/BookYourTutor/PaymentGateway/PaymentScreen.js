import {
  ActivityIndicator,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useState} from 'react';
import WebView from 'react-native-webview';
import colors from '../../../Utils/colors';
import {fp} from '../../../Helper/ResponsiveDimensions';
import {showToast} from '../../../Components/ToastHelper';
import {AppHeader} from '../../../Components/Header';
import icons from '../../../Utils/icons';
import {useTranslation} from 'react-i18next';

const PaymentScreen = ({navigation, route}) => {
  const {url} = route?.params;
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  console.log('🚀 ~ PaymentScreen ~ url:', url);
  function onChange(params) {
    console.log(params, 'asdf');
    if (
      params?.url.includes('statusId=2') &&
      (params?.url?.includes('custom1=booking_payment') ||
        params?.url?.includes('custom1=open_session_payment'))
    ) {
      navigation?.navigate('PaymentSuccessfull');
    } else if (params?.url?.includes('status=Failed')) {
      showToast('error', t('paymentFailed'), 'bottom', isRTL);
      navigation.goBack();
    } else if (
      params?.url?.includes('custom1=wallet') &&
      params?.url.includes('statusId=2')
    ) {
      showToast('success', t('Currency added to wallet'), 'bottom', isRTL);
      navigation.goBack();
    }
  }
  function handleError(params) {
    console.log(params, 'Error Params');
  }
  const [loading, setLoading] = useState(false);
  const hideSpinner = () => {
    setLoading(false);
  };
  const handleShouldStartLoad = request => {
    const url = request.url;

    if (url.startsWith('com.taleem.llc://')) {
      console.log('Deep Link URL:', url);
      navigation.navigate('Home');

      // Add your logic to handle the deep link here
      return false; // Prevent WebView from loading the URL
    }

    return true; // Allow WebView to load other URLs
  };
  const loader = () => (
    <ActivityIndicator
      style={{position: 'absolute', left: 0, right: 0, bottom: 0, top: 0}}
      size="large"
      color={colors.themeColor}
    />
  );
  return (
    <SafeAreaView style={{flex: 1}}>
      <AppHeader
        backIcon={icons.backIcon}
        isBackBtn
        title={t('payment')}
        style={{backgroundColor: colors.white}}
        isWhite={true}
      />
      <WebView
        source={{uri: url}}
        style={{flex: 1}}
        onNavigationStateChange={params => {
          onChange(params);
        }}
        onShouldStartLoadWithRequest={handleShouldStartLoad}
        onError={handleError}
        startInLoadingState={true}
        renderLoading={loader}
        onLoad={() => hideSpinner()}
      />
    </SafeAreaView>
  );
};

export default PaymentScreen;

const styles = StyleSheet.create({});
